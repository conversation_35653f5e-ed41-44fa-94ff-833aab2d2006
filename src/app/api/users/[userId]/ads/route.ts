import { NextRequest, NextResponse } from 'next/server';
import { db, ads, categories, breeds, regions, adImages } from '@/db';
import { eq, desc, asc } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get user's ads with related data
    const userAds = await db
      .select({
        id: ads.id,
        title: ads.title,
        adType: ads.adType,
        country: ads.country,
        priceType: ads.priceType,
        priceAmount: ads.priceAmount,
        description: ads.description,
        contactPhone: ads.contactPhone,
        createdAt: ads.createdAt,
        updatedAt: ads.updatedAt,
        isActive: ads.isActive,
        slug: ads.slug,
        category: {
          id: categories.id,
          name: categories.name,
          slug: categories.slug,
        },
        breed: {
          id: breeds.id,
          name: breeds.name,
          slug: breeds.slug,
        },
        region: {
          id: regions.id,
          name: regions.name,
          slug: regions.slug,
        },
      })
      .from(ads)
      .leftJoin(categories, eq(ads.categoryId, categories.id))
      .leftJoin(breeds, eq(ads.breedId, breeds.id))
      .leftJoin(regions, eq(ads.regionId, regions.id))
      .where(eq(ads.userId, userId))
      .orderBy(desc(ads.createdAt));

    // Get images for each ad
    const adsWithImages = await Promise.all(
      userAds.map(async (ad) => {
        const images = await db
          .select()
          .from(adImages)
          .where(eq(adImages.adId, ad.id))
          .orderBy(asc(adImages.order));
        
        return {
          ...ad,
          images,
        };
      })
    );

    return NextResponse.json({
      ads: adsWithImages,
      count: adsWithImages.length,
    });
  } catch (error) {
    console.error('Error fetching user ads:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user ads' },
      { status: 500 }
    );
  }
}
