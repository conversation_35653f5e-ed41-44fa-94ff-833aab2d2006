import { NextRequest, NextResponse } from 'next/server';
import { db, ads, users } from '@/db';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { fromUserId, toUserId } = body;

    if (!fromUserId || !toUserId) {
      return NextResponse.json(
        { error: 'Both fromUserId and toUserId are required' },
        { status: 400 }
      );
    }

    console.log('Migrating ads:', { fromUserId, toUserId });

    // First, ensure the target user exists in the users table
    try {
      await db
        .insert(users)
        .values({
          id: toUserId,
          phoneNumber: null, // Will be updated from the first ad if needed
        })
        .onConflictDoNothing();
    } catch (userError) {
      console.log('Target user might already exist:', userError);
    }

    // Get ads to migrate
    const adsToMigrate = await db
      .select()
      .from(ads)
      .where(eq(ads.userId, fromUserId));

    console.log(`Found ${adsToMigrate.length} ads to migrate`);

    if (adsToMigrate.length === 0) {
      return NextResponse.json({
        success: true,
        migratedCount: 0,
        message: 'No ads to migrate',
      });
    }

    // Update all ads to use the new user ID
    const result = await db
      .update(ads)
      .set({
        userId: toUserId,
        updatedAt: new Date(),
      })
      .where(eq(ads.userId, fromUserId))
      .returning();

    console.log(`Successfully migrated ${result.length} ads`);

    // Update user phone number if not set and we have it from ads
    if (adsToMigrate.length > 0 && adsToMigrate[0].contactPhone) {
      try {
        await db
          .update(users)
          .set({
            phoneNumber: adsToMigrate[0].contactPhone,
          })
          .where(eq(users.id, toUserId));
      } catch (phoneError) {
        console.log('Could not update user phone:', phoneError);
      }
    }

    return NextResponse.json({
      success: true,
      migratedCount: result.length,
      message: `Successfully migrated ${result.length} ads from guest account to authenticated account`,
    });
  } catch (error) {
    console.error('Error migrating ads:', error);
    return NextResponse.json(
      { error: 'Failed to migrate ads' },
      { status: 500 }
    );
  }
}
