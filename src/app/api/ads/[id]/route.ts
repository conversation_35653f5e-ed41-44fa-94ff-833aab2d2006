import { NextRequest, NextResponse } from 'next/server';
import { db, ads, categories, breeds, regions, users, adImages } from '@/db';
import { eq } from 'drizzle-orm';
import { generateSlug } from '@/lib/utils';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const adId = parseInt(id);

    if (isNaN(adId)) {
      return NextResponse.json(
        { error: 'Invalid ad ID' },
        { status: 400 }
      );
    }
    
    const [ad] = await db
      .select({
        id: ads.id,
        title: ads.title,
        adType: ads.adType,
        country: ads.country,
        priceType: ads.priceType,
        priceAmount: ads.priceAmount,
        description: ads.description,
        contactPhone: ads.contactPhone,
        userId: ads.userId,
        createdAt: ads.createdAt,
        updatedAt: ads.updatedAt,
        isActive: ads.isActive,
        slug: ads.slug,
        categoryId: ads.categoryId,
        breedId: ads.breedId,
        regionId: ads.regionId,
        category: {
          id: categories.id,
          name: categories.name,
          slug: categories.slug,
        },
        breed: {
          id: breeds.id,
          name: breeds.name,
          slug: breeds.slug,
        },
        region: {
          id: regions.id,
          name: regions.name,
          slug: regions.slug,
        },
        user: {
          id: users.id,
          phoneNumber: users.phoneNumber,
        },
      })
      .from(ads)
      .leftJoin(categories, eq(ads.categoryId, categories.id))
      .leftJoin(breeds, eq(ads.breedId, breeds.id))
      .leftJoin(regions, eq(ads.regionId, regions.id))
      .leftJoin(users, eq(ads.userId, users.id))
      .where(eq(ads.id, adId));
    
    if (!ad) {
      return NextResponse.json(
        { error: 'Ad not found' },
        { status: 404 }
      );
    }
    
    // Get images
    const images = await db
      .select()
      .from(adImages)
      .where(eq(adImages.adId, adId))
      .orderBy(adImages.order);
    
    return NextResponse.json({
      ...ad,
      images,
    });
  } catch (error) {
    console.error('Error fetching ad:', error);
    return NextResponse.json(
      { error: 'Failed to fetch ad' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const adId = parseInt(id);

    if (isNaN(adId)) {
      return NextResponse.json(
        { error: 'Invalid ad ID' },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Check if ad exists and get current data
    const [existingAd] = await db
      .select()
      .from(ads)
      .where(eq(ads.id, adId));

    if (!existingAd) {
      return NextResponse.json(
        { error: 'Ad not found' },
        { status: 404 }
      );
    }

    // Verify user ownership
    if (body.userId && existingAd.userId !== body.userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Generate new slug if title changed
    const slug = body.title !== existingAd.title ? generateSlug(body.title) : existingAd.slug;

    // Prepare update data - only include fields that are provided
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (body.title !== undefined) {
      updateData.title = body.title;
      updateData.slug = slug;
    }
    if (body.adType !== undefined) updateData.adType = body.adType;
    if (body.country !== undefined) updateData.country = body.country;
    if (body.regionId !== undefined) updateData.regionId = body.regionId || null;
    if (body.categoryId !== undefined) updateData.categoryId = body.categoryId;
    if (body.breedId !== undefined) updateData.breedId = body.breedId || null;
    if (body.priceType !== undefined) updateData.priceType = body.priceType || null;
    if (body.priceAmount !== undefined) updateData.priceAmount = body.priceAmount || null;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.contactPhone !== undefined) updateData.contactPhone = body.contactPhone;
    if (body.isActive !== undefined) updateData.isActive = body.isActive;

    // Update ad
    const [updatedAd] = await db
      .update(ads)
      .set(updateData)
      .where(eq(ads.id, adId))
      .returning();

    return NextResponse.json(updatedAd);
  } catch (error) {
    console.error('Error updating ad:', error);
    return NextResponse.json(
      { error: 'Failed to update ad' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const adId = parseInt(id);

    if (isNaN(adId)) {
      return NextResponse.json(
        { error: 'Invalid ad ID' },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Check if ad exists and verify ownership
    const [existingAd] = await db
      .select()
      .from(ads)
      .where(eq(ads.id, adId));

    if (!existingAd) {
      return NextResponse.json(
        { error: 'Ad not found' },
        { status: 404 }
      );
    }

    if (existingAd.userId !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Delete ad (images will be deleted automatically due to cascade)
    await db
      .delete(ads)
      .where(eq(ads.id, adId));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting ad:', error);
    return NextResponse.json(
      { error: 'Failed to delete ad' },
      { status: 500 }
    );
  }
}
