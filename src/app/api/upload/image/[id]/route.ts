import { NextRequest, NextResponse } from 'next/server';
import { db, adImages } from '@/db';
import { eq } from 'drizzle-orm';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const imageId = parseInt(id);
    
    if (isNaN(imageId)) {
      return NextResponse.json(
        { error: 'Invalid image ID' },
        { status: 400 }
      );
    }

    // Get image data before deletion
    const [image] = await db
      .select()
      .from(adImages)
      .where(eq(adImages.id, imageId));

    if (!image) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    // Extract file path from URL for Supabase storage deletion
    const url = new URL(image.imageUrl);
    const pathParts = url.pathname.split('/');
    const filePath = pathParts.slice(-2).join('/'); // Get last two parts: ad-images/filename

    // Delete from Supabase Storage
    try {
      const { error: storageError } = await supabase.storage
        .from('images')
        .remove([filePath]);

      if (storageError) {
        console.error('Supabase storage deletion error:', storageError);
        // Continue with database deletion even if storage deletion fails
      }
    } catch (storageError) {
      console.error('Error deleting from storage:', storageError);
      // Continue with database deletion
    }

    // Delete from database
    await db
      .delete(adImages)
      .where(eq(adImages.id, imageId));

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting image:', error);
    return NextResponse.json(
      { error: 'Failed to delete image' },
      { status: 500 }
    );
  }
}
