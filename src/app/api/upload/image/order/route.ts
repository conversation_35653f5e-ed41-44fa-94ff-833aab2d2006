import { NextRequest, NextResponse } from 'next/server';
import { db, adImages } from '@/db';
import { eq } from 'drizzle-orm';

interface ImageOrderUpdate {
  id: number;
  order: number;
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { images }: { images: ImageOrderUpdate[] } = body;

    if (!images || !Array.isArray(images)) {
      return NextResponse.json(
        { error: 'Images array is required' },
        { status: 400 }
      );
    }

    // Validate that all images have id and order
    for (const image of images) {
      if (typeof image.id !== 'number' || typeof image.order !== 'number') {
        return NextResponse.json(
          { error: 'Each image must have id and order' },
          { status: 400 }
        );
      }
    }

    // Update each image's order in the database
    const updatePromises = images.map(async (image) => {
      return db
        .update(adImages)
        .set({ order: image.order })
        .where(eq(adImages.id, image.id))
        .returning();
    });

    const results = await Promise.all(updatePromises);

    // Check if all updates were successful
    const updatedImages = results.flat();
    if (updatedImages.length !== images.length) {
      return NextResponse.json(
        { error: 'Some images could not be updated' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      updatedImages,
    });
  } catch (error) {
    console.error('Error updating image order:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
