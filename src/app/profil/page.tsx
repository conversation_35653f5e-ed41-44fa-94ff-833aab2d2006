'use client';

import { Button } from '@/components/ui/Button';
import { User, Settings, Plus } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/components/auth/AuthProvider';
import { useUserId } from '@/hooks/useUserId';
import { UserAds } from '@/components/profile/UserAds';

export default function ProfilePage() {
  const { user, loading } = useAuth();
  const { userId, isAuthenticated, isGuest } = useUserId();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Načítání...</p>
        </div>
      </div>
    );
  }

  // Allow access for both authenticated users and guests with stored userId
  if (!user && !isGuest) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow p-6 text-center">
          <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Přihlášení vyžadováno
          </h1>
          <p className="text-gray-600 mb-6">
            Pro přístup k profilu se musíte nejprve přihlásit nebo vytvořit inzerát.
          </p>
          <div className="space-y-3">
            <Button className="w-full" asChild>
              <Link href="/auth/login">
                Přihlásit se
              </Link>
            </Button>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/auth/signup">
                Registrovat se
              </Link>
            </Button>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/pridat-inzerat">
                Přidat inzerát jako host
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Můj profil
              </h1>
              <p className="text-lg text-gray-600">
                Spravujte své inzeráty a nastavení
              </p>
            </div>
            <Button asChild>
              <Link href="/pridat-inzerat">
                <Plus className="w-4 h-4 mr-2" />
                Přidat inzerát
              </Link>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <User className="w-10 h-10 text-gray-400" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {user?.user_metadata?.full_name || (isGuest ? 'Host uživatel' : 'Uživatel')}
                </h2>
                <p className="text-gray-600">
                  {user?.email || (isGuest ? 'Nepřihlášený uživatel' : '')}
                </p>
              </div>
              
              <nav className="space-y-2">
                <a href="#" className="flex items-center px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">
                  <User className="w-4 h-4 mr-3" />
                  Profil
                </a>
                <a href="#" className="flex items-center px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">
                  <Settings className="w-4 h-4 mr-3" />
                  Nastavení
                </a>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* My Ads */}
            <UserAds />
          </div>
        </div>
      </div>
    </div>
  );
}
