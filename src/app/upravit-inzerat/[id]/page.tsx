import { Metadata } from 'next';
import { EditAdForm } from '@/components/forms/EditAdForm';
import { notFound } from 'next/navigation';

interface EditAdPageProps {
  params: Promise<{ id: string }>;
}

export const metadata: Metadata = {
  title: 'Upravit inzerát - Infauna',
  description: 'Upravte váš inzerát pro prodej, koupi nebo darování zvířat.',
};

export default async function EditAdPage({ params }: EditAdPageProps) {
  const { id } = await params;
  const adId = parseInt(id);

  if (isNaN(adId)) {
    notFound();
  }

  return <EditAdForm adId={adId} />;
}
