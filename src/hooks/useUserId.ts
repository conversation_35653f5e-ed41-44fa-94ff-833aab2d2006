'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/auth/AuthProvider';

const USER_ID_KEY = 'infauna_user_id';

// Function to migrate ads from guest ID to authenticated user ID
async function migrateGuestAds(guestId: string, authId: string) {
  try {
    console.log('Migrating ads from guest ID to auth ID:', { guestId, authId });

    const response = await fetch('/api/users/migrate-ads', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fromUserId: guestId,
        toUserId: authId,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to migrate ads');
    }

    const result = await response.json();
    console.log('Migration result:', result);
  } catch (error) {
    console.error('Error migrating ads:', error);
  }
}

export function useUserId() {
  const { user } = useAuth();
  const [guestUserId, setGuestUserId] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // If user is authenticated, use their auth ID
      if (user?.id) {
        // Check if there was a guest ID before login
        const storedGuestId = localStorage.getItem(USER_ID_KEY);
        if (storedGuestId) {
          // Migrate ads from guest ID to auth ID
          migrateGuestAds(storedGuestId, user.id);
          // Remove guest ID from localStorage after migration
          localStorage.removeItem(USER_ID_KEY);
        }
        setGuestUserId(null);
        return;
      }

      // For guest users, get or create a persistent ID
      let storedUserId = localStorage.getItem(USER_ID_KEY);
      if (!storedUserId) {
        storedUserId = crypto.randomUUID();
        localStorage.setItem(USER_ID_KEY, storedUserId);
      }
      setGuestUserId(storedUserId);
    }
  }, [user]);

  // Return the appropriate user ID
  const userId = user?.id || guestUserId;
  const isAuthenticated = !!user;
  const isGuest = !user && !!guestUserId;

  return {
    userId,
    isAuthenticated,
    isGuest,
    clearGuestId: () => {
      if (typeof window !== 'undefined') {
        localStorage.removeItem(USER_ID_KEY);
        setGuestUserId(null);
      }
    },
  };
}
