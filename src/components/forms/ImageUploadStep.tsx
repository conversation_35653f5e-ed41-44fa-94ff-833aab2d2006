import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/Button';
import {
  Upload,
  X,
  Image as ImageIcon,
  Camera,
  AlertCircle,
  CheckCircle,
  GripVertical,
  Star
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ImageFile {
  id: string;
  file: File;
  preview: string;
  uploading?: boolean;
  uploaded?: boolean;
  error?: string;
}

interface ExistingImage {
  id: number;
  imageUrl: string;
  order: number;
  deleting?: boolean;
  dragging?: boolean;
}

interface ImageUploadStepProps {
  adId: number;
  existingImages?: ExistingImage[];
  onImagesUploaded?: (imageUrls: string[]) => void;
  onImageDeleted?: (imageId: number) => void;
  onImageOrderChanged?: (images: ExistingImage[]) => void;
}

export function ImageUploadStep({
  adId,
  existingImages = [],
  onImagesUploaded,
  onImageDeleted,
  onImageOrderChanged
}: ImageUploadStepProps) {
  const [images, setImages] = useState<ImageFile[]>([]);
  const [existingImagesState, setExistingImagesState] = useState<ExistingImage[]>(existingImages);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [draggedImageId, setDraggedImageId] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());

  const maxImages = 10;
  const maxFileSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  // Update existing images when prop changes
  React.useEffect(() => {
    setExistingImagesState(existingImages);
    // Reset image errors when images change
    setImageErrors(new Set());
  }, [existingImages]);

  const validateFile = (file: File): string | null => {
    if (!allowedTypes.includes(file.type)) {
      return 'Podporované formáty: JPG, PNG, WebP';
    }
    if (file.size > maxFileSize) {
      return 'Maximální velikost souboru je 5MB';
    }
    return null;
  };

  const handleFiles = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const newImages: ImageFile[] = [];

    fileArray.forEach((file) => {
      if (images.length + newImages.length >= maxImages) return;

      const error = validateFile(file);
      if (error) {
        // Show error toast or notification
        console.error(`Error with file ${file.name}: ${error}`);
        return;
      }

      const id = Math.random().toString(36).substr(2, 9);
      const preview = URL.createObjectURL(file);
      
      newImages.push({
        id,
        file,
        preview,
      });
    });

    setImages(prev => [...prev, ...newImages]);
  }, [images.length]);

  const handleFileDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);

    if (e.dataTransfer.files) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleFileDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  }, []);

  const handleFileDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  const removeImage = (id: string) => {
    setImages(prev => {
      const image = prev.find(img => img.id === id);
      if (image) {
        URL.revokeObjectURL(image.preview);
      }
      return prev.filter(img => img.id !== id);
    });
  };

  const deleteExistingImage = async (imageId: number) => {
    // Set deleting state
    setExistingImagesState(prev =>
      prev.map(img =>
        img.id === imageId ? { ...img, deleting: true } : img
      )
    );

    try {
      const response = await fetch(`/api/upload/image/${imageId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete image');
      }

      // Remove from state
      setExistingImagesState(prev => prev.filter(img => img.id !== imageId));

      // Notify parent component
      if (onImageDeleted) {
        onImageDeleted(imageId);
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      // Reset deleting state on error
      setExistingImagesState(prev =>
        prev.map(img =>
          img.id === imageId ? { ...img, deleting: false } : img
        )
      );
      alert('Nepodařilo se smazat obrázek');
    }
  };

  const updateImageOrder = async (newOrder: ExistingImage[]) => {
    try {
      const response = await fetch('/api/upload/image/order', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          images: newOrder.map((img, index) => ({
            id: img.id,
            order: index,
          })),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update image order');
      }

      // Update local state with new order
      const updatedImages = newOrder.map((img, index) => ({
        ...img,
        order: index,
      }));

      setExistingImagesState(updatedImages);

      // Notify parent component
      if (onImageOrderChanged) {
        onImageOrderChanged(updatedImages);
      }
    } catch (error) {
      console.error('Error updating image order:', error);
      alert('Nepodařilo se aktualizovat pořadí fotografií');
    }
  };

  const handleDragStart = (e: React.DragEvent, imageId: number) => {
    setDraggedImageId(imageId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleImageDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  };

  const handleImageDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleImageDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (draggedImageId === null) return;

    const draggedIndex = existingImagesState.findIndex(img => img.id === draggedImageId);
    if (draggedIndex === -1) return;

    const newImages = [...existingImagesState];
    const draggedImage = newImages[draggedIndex];

    // Remove dragged image from its current position
    newImages.splice(draggedIndex, 1);

    // Insert at new position
    newImages.splice(dropIndex, 0, draggedImage);

    // Update order in database
    updateImageOrder(newImages);

    // Reset drag state
    setDraggedImageId(null);
    setDragOverIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedImageId(null);
    setDragOverIndex(null);
  };

  const uploadImages = async () => {
    if (images.length === 0) return;

    setUploading(true);
    const uploadedUrls: string[] = [];

    try {
      for (let i = 0; i < images.length; i++) {
        const image = images[i];
        
        // Update image state to show uploading
        setImages(prev => prev.map(img => 
          img.id === image.id ? { ...img, uploading: true } : img
        ));

        const formData = new FormData();
        formData.append('file', image.file);
        formData.append('adId', adId.toString());
        formData.append('order', i.toString());

        try {
          const response = await fetch('/api/upload/image', {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            throw new Error('Upload failed');
          }

          const result = await response.json();
          uploadedUrls.push(result.imageUrl);

          // Update image state to show success
          setImages(prev => prev.map(img => 
            img.id === image.id 
              ? { ...img, uploading: false, uploaded: true } 
              : img
          ));
        } catch (error) {
          // Update image state to show error
          setImages(prev => prev.map(img => 
            img.id === image.id 
              ? { ...img, uploading: false, error: 'Upload failed' } 
              : img
          ));
        }
      }

      onImagesUploaded?.(uploadedUrls);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-xl font-bold text-gray-900 mb-2">
          {existingImagesState.length > 0 ? 'Správa fotografií' : 'Přidejte fotografie'}
        </h2>
        <p className="text-gray-600 text-sm">
          Fotografie pomohou vašemu inzerátu získat více pozornosti
        </p>
      </div>

      {/* Existing Images */}
      {existingImagesState.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Současné fotografie ({existingImagesState.length})
            </h3>
            <p className="text-sm text-gray-500">
              Přetáhněte pro změnu pořadí • První = náhledová
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {existingImagesState.map((image, index) => (
              <div
                key={image.id}
                className={cn(
                  "relative group cursor-move",
                  dragOverIndex === index && "ring-2 ring-primary-500 ring-offset-2",
                  draggedImageId === image.id && "opacity-50"
                )}
                draggable
                onDragStart={(e) => handleDragStart(e, image.id)}
                onDragOver={(e) => handleImageDragOver(e, index)}
                onDragLeave={handleImageDragLeave}
                onDrop={(e) => handleImageDrop(e, index)}
                onDragEnd={handleDragEnd}
              >
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 relative">
                  {imageErrors.has(image.id) ? (
                    <div className="w-full h-full flex items-center justify-center bg-gray-200">
                      <div className="text-center">
                        <ImageIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-xs text-gray-500">Chyba načítání</p>
                        <button
                          className="text-xs text-blue-500 hover:text-blue-700 mt-1"
                          onClick={() => {
                            setImageErrors(prev => {
                              const newSet = new Set(prev);
                              newSet.delete(image.id);
                              return newSet;
                            });
                          }}
                        >
                          Zkusit znovu
                        </button>
                      </div>
                    </div>
                  ) : (
                    <img
                      src={image.imageUrl}
                      alt="Existing image"
                      className="w-full h-full object-cover"
                      onError={() => {
                        console.error('Failed to load image:', image.imageUrl);
                        setImageErrors(prev => new Set(prev).add(image.id));
                      }}
                      onLoad={() => {
                        // Remove from error set if it loads successfully
                        setImageErrors(prev => {
                          const newSet = new Set(prev);
                          newSet.delete(image.id);
                          return newSet;
                        });
                      }}
                    />
                  )}

                  {/* Primary image indicator */}
                  {index === 0 && (
                    <div className="absolute top-2 left-2">
                      <div className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                        <Star className="w-3 h-3 mr-1" />
                        Náhled
                      </div>
                    </div>
                  )}

                  {/* Drag handle */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="bg-black bg-opacity-50 rounded p-1">
                      <GripVertical className="w-4 h-4 text-white" />
                    </div>
                  </div>

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                    <Button
                      variant="destructive"
                      size="icon"
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => deleteExistingImage(image.id)}
                      disabled={image.deleting}
                    >
                      {image.deleting ? (
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <X className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload Area - only show if there's space for more images */}
      {(existingImagesState.length + images.length) < maxImages && (
        <div
          className={cn(
            'border-2 border-dashed rounded-lg p-6 text-center transition-colors',
            dragActive
              ? 'border-primary-500 bg-primary-50'
              : 'border-gray-300 hover:border-gray-400'
          )}
          onDrop={handleFileDrop}
          onDragOver={handleFileDragOver}
          onDragLeave={handleFileDragLeave}
        >
        <div className="space-y-4">
          <div className="flex justify-center">
            <div className="p-3 bg-gray-100 rounded-full">
              <Camera className="w-8 h-8 text-gray-600" />
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Přetáhněte fotografie sem
            </h3>
            <p className="text-gray-600 mb-4">
              nebo klikněte pro výběr souborů
            </p>
            
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileInput}
              className="hidden"
              id="file-upload"
            />
            
            <Button asChild variant="outline">
              <label htmlFor="file-upload" className="cursor-pointer">
                <Upload className="w-4 h-4 mr-2" />
                Vybrat fotografie
              </label>
            </Button>
          </div>
          
          <p className="text-xs text-gray-500">
            Maximálně {maxImages} fotografií, každá do 5MB (JPG, PNG, WebP)
          </p>
        </div>
        </div>
      )}

      {/* Image Preview Grid */}
      {images.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Vybrané fotografie ({images.length}/{maxImages})
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((image) => (
              <div key={image.id} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={image.preview}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                    <Button
                      variant="destructive"
                      size="icon"
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => removeImage(image.id)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  {/* Status Indicator */}
                  <div className="absolute top-2 right-2">
                    {image.uploading && (
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      </div>
                    )}
                    {image.uploaded && (
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-white" />
                      </div>
                    )}
                    {image.error && (
                      <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                        <AlertCircle className="w-4 h-4 text-white" />
                      </div>
                    )}
                  </div>
                </div>
                
                {image.error && (
                  <p className="text-xs text-red-600 mt-1">{image.error}</p>
                )}
              </div>
            ))}
          </div>
          
          {images.length > 0 && !uploading && (
            <div className="flex justify-center">
              <Button onClick={uploadImages} size="lg">
                <Upload className="w-4 h-4 mr-2" />
                Nahrát fotografie
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
