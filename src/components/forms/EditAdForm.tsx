'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { BasicInfoStep } from './BasicInfoStep';
import { ImageUploadStep } from './ImageUploadStep';
import { AdFormData, AdFormErrors } from '@/types/ad-form';
import { ArrowLeft, Save, Loader2, Images } from 'lucide-react';
import { useUserId } from '@/hooks/useUserId';

interface AdImage {
  id: number;
  imageUrl: string;
  order: number;
}

interface EditAdFormProps {
  adId: number;
}

export function EditAdForm({ adId }: EditAdFormProps) {
  const router = useRouter();
  const { userId } = useUserId();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentTab, setCurrentTab] = useState<'basic' | 'images'>('basic');
  const [adImages, setAdImages] = useState<AdImage[]>([]);

  const [formData, setFormData] = useState<AdFormData>({
    title: '',
    adType: 'prodám',
    description: '',
    country: 'ČR',
    categoryId: 0,
    contactPhone: '',
  });

  const [errors, setErrors] = useState<AdFormErrors>({});

  useEffect(() => {
    if (adId) {
      fetchAdData();
    }
  }, [adId]);

  const fetchAdData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/ads/${adId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch ad data');
      }

      const ad = await response.json();
      
      // Check if user owns this ad
      if (userId && ad.userId !== userId) {
        setError('Nemáte oprávnění upravovat tento inzerát');
        return;
      }

      setFormData({
        title: ad.title,
        adType: ad.adType,
        description: ad.description,
        country: ad.country,
        categoryId: ad.categoryId,
        breedId: ad.breedId || undefined,
        regionId: ad.regionId || undefined,
        priceType: ad.priceType || undefined,
        priceAmount: ad.priceAmount || undefined,
        contactPhone: ad.contactPhone,
      });

      // Set images
      setAdImages(ad.images || []);
    } catch (err) {
      console.error('Error fetching ad data:', err);
      setError('Nepodařilo se načíst data inzerátu');
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (field: keyof AdFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: AdFormErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Název je povinný';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Popis je povinný';
    }

    if (!formData.categoryId || formData.categoryId === 0) {
      newErrors.categoryId = 'Kategorie je povinná';
    }

    if (!formData.contactPhone.trim()) {
      newErrors.contactPhone = 'Kontaktní telefon je povinný';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !userId) return;

    try {
      setSaving(true);
      const response = await fetch(`/api/ads/${adId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          userId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update ad');
      }

      const updatedAd = await response.json();
      
      // Redirect to ad detail page
      router.push(`/inzerat/${updatedAd.slug}`);
    } catch (err) {
      console.error('Error updating ad:', err);
      setErrors({ title: 'Chyba při ukládání inzerátu. Zkuste to prosím znovu.' });
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleImageDeleted = (imageId: number) => {
    setAdImages(prev => prev.filter(img => img.id !== imageId));
  };

  const handleImagesUploaded = (imageUrls: string[]) => {
    // Refresh ad data to get updated images
    fetchAdData();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Načítání inzerátu...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={handleBack}>
              Zpět
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-3">
            Upravit inzerát
          </h1>
          <p className="text-lg text-gray-600">
            Upravte informace o vašem inzerátu
          </p>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-lg">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setCurrentTab('basic')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  currentTab === 'basic'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Základní informace
              </button>
              <button
                onClick={() => setCurrentTab('images')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  currentTab === 'images'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Images className="w-4 h-4 mr-2 inline" />
                Fotografie ({adImages.length})
              </button>
            </nav>
          </div>

          <div className="p-6">
            {currentTab === 'basic' && (
              <BasicInfoStep
                data={formData}
                errors={errors}
                onChange={handleFieldChange}
              />
            )}

            {currentTab === 'images' && (
              <ImageUploadStep
                adId={adId}
                existingImages={adImages}
                onImageDeleted={handleImageDeleted}
                onImagesUploaded={handleImagesUploaded}
              />
            )}
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center px-6 py-4 border-t bg-gray-50">
            <Button
              variant="outline"
              onClick={handleBack}
              disabled={saving}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Zpět
            </Button>

            {currentTab === 'basic' && (
              <Button
                onClick={handleSubmit}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Ukládání...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Uložit změny
                  </>
                )}
              </Button>
            )}

            {currentTab === 'images' && (
              <Button
                onClick={() => router.push(`/inzerat/${adId}`)}
                variant="outline"
              >
                Zobrazit inzerát
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
