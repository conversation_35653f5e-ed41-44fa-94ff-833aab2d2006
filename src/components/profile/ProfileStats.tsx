'use client';

import React from 'react';
import { Eye, Calendar, CheckCircle, XCircle } from 'lucide-react';

interface UserAd {
  id: number;
  isActive: boolean;
  createdAt: string;
}

interface ProfileStatsProps {
  ads: UserAd[];
}

export function ProfileStats({ ads }: ProfileStatsProps) {
  const totalAds = ads.length;
  const activeAds = ads.filter(ad => ad.isActive).length;
  const inactiveAds = totalAds - activeAds;
  
  // Calculate days since first ad
  const daysSinceFirstAd = ads.length > 0 
    ? Math.floor((Date.now() - new Date(ads[ads.length - 1].createdAt).getTime()) / (1000 * 60 * 60 * 24))
    : 0;

  const stats = [
    {
      label: 'Celkem inzerátů',
      value: totalAds,
      icon: Eye,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      label: 'Aktivní inzeráty',
      value: activeAds,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      label: 'Neaktivní inzeráty',
      value: inactiveAds,
      icon: XCircle,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
    },
    {
      label: 'Dní na platformě',
      value: daysSinceFirstAd,
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Přehled
      </h3>
      
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="text-center">
              <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center mx-auto mb-2`}>
                <Icon className={`w-6 h-6 ${stat.color}`} />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stat.value}
              </div>
              <div className="text-sm text-gray-600">
                {stat.label}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
