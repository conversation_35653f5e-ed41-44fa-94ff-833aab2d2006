'use client';

import React from 'react';
import { Modal } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { MapPin, Calendar, ExternalLink, Edit } from 'lucide-react';
import Link from 'next/link';

interface AdImage {
  id: number;
  imageUrl: string;
  order: number;
}

interface UserAd {
  id: number;
  title: string;
  adType: string;
  country: string;
  priceType?: string;
  priceAmount?: string;
  description: string;
  contactPhone: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  slug: string;
  category: {
    id: number;
    name: string;
    slug: string;
  };
  breed?: {
    id: number;
    name: string;
    slug: string;
  };
  region?: {
    id: number;
    name: string;
    slug: string;
  };
  images: AdImage[];
}

interface AdPreviewModalProps {
  ad: UserAd | null;
  isOpen: boolean;
  onClose: () => void;
}

export function AdPreviewModal({ ad, isOpen, onClose }: AdPreviewModalProps) {
  if (!ad) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('cs-CZ', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const formatPrice = (priceType?: string, priceAmount?: string) => {
    if (!priceType) return null;
    
    if (priceType === 'Dohodou' || priceType === 'V textu') {
      return priceType;
    }
    
    if (priceAmount && priceType) {
      return `${priceAmount} ${priceType}`;
    }
    
    return priceType;
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Náhled inzerátu"
      size="lg"
    >
      <div className="space-y-6">
        {/* Image */}
        {ad.images.length > 0 && (
          <div className="w-full h-48 bg-gray-100 rounded-lg overflow-hidden">
            <img
              src={ad.images[0].imageUrl}
              alt={ad.title}
              className="w-full h-full object-cover"
            />
          </div>
        )}

        {/* Title and Type */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-xl font-bold text-gray-900">
              {ad.title}
            </h2>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              ad.isActive 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {ad.isActive ? 'Aktivní' : 'Neaktivní'}
            </span>
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded-full font-medium">
              {ad.adType}
            </span>
            <span>{ad.category.name}</span>
            {ad.breed && (
              <>
                <span>•</span>
                <span>{ad.breed.name}</span>
              </>
            )}
          </div>
        </div>

        {/* Price */}
        {formatPrice(ad.priceType, ad.priceAmount) && (
          <div className="text-2xl font-bold text-primary-600">
            {formatPrice(ad.priceType, ad.priceAmount)}
          </div>
        )}

        {/* Description */}
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">Popis</h3>
          <p className="text-gray-700 whitespace-pre-wrap">
            {ad.description}
          </p>
        </div>

        {/* Details */}
        <div className="space-y-2">
          <div className="flex items-center text-gray-600">
            <MapPin className="w-4 h-4 mr-2" />
            <span>{ad.country}</span>
            {ad.region && (
              <>
                <span className="mx-1">•</span>
                <span>{ad.region.name}</span>
              </>
            )}
          </div>
          
          <div className="flex items-center text-gray-600">
            <Calendar className="w-4 h-4 mr-2" />
            <span>Vytvořeno {formatDate(ad.createdAt)}</span>
          </div>
        </div>

        {/* Contact */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-2">Kontakt</h3>
          <p className="text-gray-700 font-mono">{ad.contactPhone}</p>
        </div>

        {/* Actions */}
        <div className="flex space-x-3 pt-4 border-t">
          <Button asChild className="flex-1">
            <Link href={`/inzerat/${ad.slug}`}>
              <ExternalLink className="w-4 h-4 mr-2" />
              Zobrazit veřejně
            </Link>
          </Button>
          <Button asChild variant="outline" className="flex-1">
            <Link href={`/upravit-inzerat/${ad.id}`}>
              <Edit className="w-4 h-4 mr-2" />
              Upravit
            </Link>
          </Button>
        </div>
      </div>
    </Modal>
  );
}
